@page "/login"
@using Microsoft.AspNetCore.Identity
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Components.Shared
@using System.ComponentModel.DataAnnotations

@inherits LocalizedComponentBase
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>@GetPageTitle("Login", "تسجيل الدخول")</PageTitle>

<div class="login-page">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary">
                <div class="text-center text-white p-5">
                    <div class="mb-4">
                        <i class="fas fa-chart-line fa-5x mb-4"></i>
                        <h1 class="display-4 fw-bold">
                            @L("Employee Rating System", "نظام تقييم الموظفين")
                        </h1>
                        <p class="lead">
                            @L("Comprehensive performance evaluation platform", "منصة شاملة لتقييم الأداء")
                        </p>
                    </div>
                    <div class="row text-center">
                        <div class="col-4">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <h6>@L("User Management", "إدارة المستخدمين")</h6>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-building fa-2x mb-2"></i>
                            <h6>@L("Departments", "الأقسام")</h6>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-star fa-2x mb-2"></i>
                            <h6>@L("Evaluations", "التقييمات")</h6>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center">
                <div class="login-form-container w-100" style="max-width: 400px;">
                    <div class="text-center mb-4">
                        <div class="d-lg-none mb-3">
                            <i class="fas fa-chart-line fa-3x text-primary"></i>
                        </div>
                        <h2 class="fw-bold">@L("Welcome Back", "مرحباً بعودتك")</h2>
                        <p class="text-muted">@L("Sign in to your account", "سجل دخولك إلى حسابك")</p>
                    </div>

                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle @GetMarginEnd(1)"></i>
                            @errorMessage
                        </div>
                    }

                    <EditForm Model="loginModel" OnValidSubmit="HandleLogin">
                        <DataAnnotationsValidator />

                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope @GetMarginEnd(1)"></i>
                                @L("Email Address", "عنوان البريد الإلكتروني")
                            </label>
                            <InputText @bind-Value="loginModel.Email" class="form-control form-control-lg"
                                      id="email" placeholder="@L("Enter your email", "أدخل بريدك الإلكتروني")" />
                            <ValidationMessage For="@(() => loginModel.Email)" />
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock @GetMarginEnd(1)"></i>
                                @L("Password", "كلمة المرور")
                            </label>
                            <div class="input-group">
                                <InputText @bind-Value="loginModel.Password" type="@(showPassword ? "text" : "password")"
                                          class="form-control form-control-lg" id="password"
                                          placeholder="@L("Enter your password", "أدخل كلمة المرور")" />
                                <button type="button" class="btn btn-outline-secondary" @onclick="TogglePasswordVisibility">
                                    <i class="fas fa-@(showPassword ? "eye-slash" : "eye")"></i>
                                </button>
                            </div>
                            <ValidationMessage For="@(() => loginModel.Password)" />
                        </div>

                        <div class="mb-3 form-check">
                            <InputCheckbox @bind-Value="loginModel.RememberMe" class="form-check-input" id="rememberMe" />
                            <label class="form-check-label" for="rememberMe">
                                @L("Remember me", "تذكرني")
                            </label>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm @GetMarginEnd(1)" role="status"></span>
                                }
                                <i class="fas fa-sign-in-alt @GetMarginEnd(1)"></i>
                                @L("Sign In", "تسجيل الدخول")
                            </button>
                        </div>
                    </EditForm>

                    <div class="text-center">
                        <div class="mb-3">
                            <a href="/forgot-password" class="text-decoration-none">
                                @L("Forgot your password?", "نسيت كلمة المرور؟")
                            </a>
                        </div>

                        <!-- Language Switcher -->
                        <div class="border-top pt-3">
                            <small class="text-muted d-block mb-2">@L("Language", "اللغة")</small>
                            <LanguageSwitcher ShowDropdown="false" Size="sm" />
                        </div>
                    </div>

                    <!-- Demo Credentials Info -->
                    @if (showDemoInfo)
                    {
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="fw-bold mb-2">
                                <i class="fas fa-info-circle @GetMarginEnd(1)"></i>
                                @L("Demo Credentials", "بيانات تجريبية")
                            </h6>
                            <small class="text-muted">
                                @L("Super Admin", "مدير النظام"): <EMAIL><br />
                                @L("Password", "كلمة المرور"): Password123!
                            </small>
                            <button type="button" class="btn btn-sm btn-outline-secondary @GetMarginStart(2)"
                                    @onclick="() => showDemoInfo = false">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    }
                    else
                    {
                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-sm btn-link text-muted"
                                    @onclick="() => showDemoInfo = true">
                                @L("Show demo credentials", "إظهار البيانات التجريبية")
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private LoginModel loginModel = new();
    private string errorMessage = string.Empty;
    private bool isLoading = false;
    private bool showPassword = false;
    private bool showDemoInfo = false;

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        var authState = await SignInManager.Context.AuthenticateAsync();
        if (authState.Succeeded)
        {
            Navigation.NavigateTo("/dashboard");
        }
    }

    private async Task HandleLogin()
    {
        isLoading = true;
        errorMessage = string.Empty;
        StateHasChanged();

        try
        {
            var result = await SignInManager.PasswordSignInAsync(
                loginModel.Email,
                loginModel.Password,
                loginModel.RememberMe,
                lockoutOnFailure: true);

            if (result.Succeeded)
            {
                // Get the user to check if they're active
                var user = await UserManager.FindByEmailAsync(loginModel.Email);
                if (user != null && user.IsActive)
                {
                    Navigation.NavigateTo("/dashboard");
                }
                else
                {
                    await SignInManager.SignOutAsync();
                    errorMessage = L("Your account has been deactivated. Please contact your administrator.",
                                   "تم إلغاء تفعيل حسابك. يرجى الاتصال بالمدير.");
                }
            }
            else if (result.IsLockedOut)
            {
                errorMessage = L("Your account has been locked due to multiple failed login attempts. Please try again later.",
                               "تم قفل حسابك بسبب محاولات دخول فاشلة متعددة. يرجى المحاولة لاحقاً.");
            }
            else if (result.RequiresTwoFactor)
            {
                // Handle two-factor authentication if implemented
                errorMessage = L("Two-factor authentication required.", "مطلوب التحقق بخطوتين.");
            }
            else
            {
                errorMessage = L("Invalid email or password. Please try again.",
                               "بريد إلكتروني أو كلمة مرور غير صحيحة. يرجى المحاولة مرة أخرى.");
            }
        }
        catch (Exception ex)
        {
            errorMessage = L("An error occurred during login. Please try again.",
                           "حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.");
            Console.WriteLine($"Login error: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void TogglePasswordVisibility()
    {
        showPassword = !showPassword;
    }

    public class LoginModel
    {
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; } = false;
    }
}