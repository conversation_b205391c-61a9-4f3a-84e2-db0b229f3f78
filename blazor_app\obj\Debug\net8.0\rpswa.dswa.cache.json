{"GlobalPropertiesHash": "AhVFzbIBhy3ag8cKlkybM9rmDT5HwQ0eHMl6MWdQ6c4=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["+AAIIgt2CT5RqLnsuxp82Ckn+ZbBL4qH37RVsS3M3iw=", "2J7kiX6tqQa90qIEPHW6V6pkDiI8KsiqO53/hRpJBgc=", "Z/HUz5GNl5yqtK3EYcbjXovB06nJfKgZ+JqKsHeHy5Q=", "tFTv17ZbLNIbUjfseZ9hqe0k0gx3ZdcoUG+ao0AHrZU=", "zvkylZYMdfXk7oa/uSO7WnsbasVogEDRpoOLoS4vhaU=", "fARBD40mEeygUn/AYRn6EpPEABSJo0M6IhgrStwX//s=", "DeS2AVaDkd8H/tXm83p5mAUdnak7mWbrlucYgx5kcRs=", "Zmb9MDV+n1VATpaQ/1Ui3zVJLUh5fy1maGYlI3qOfok=", "0rZcjawMsupbRsqYFOtTW9nOQUdxgCL8B8v79vpxvQ8=", "xwfWRCwTT+nDOAWR0E4e5N8m7Kc1/m259R1D/bma3Xo=", "kv2t3poHo7j88k+LNE7TQNFG4TrDBuDgIs7FK7xnSFM=", "ZZZJfujku+xWKkUIIKQA/Ne1MRsMtbeALDDBY5W8Rew=", "teAs9Ua3WYM7HUn4TrVF1nDnZpCy0lP/uhUaecWTEtQ=", "cHqghq0uEa4L2AaOsfCQ0z9ttLUg7/YLyesexnZTDTs=", "4AWfEB7H0zUyo79g15wCdc+Jxc5YNamloD94N99xChA=", "yhDti3AmYD31Jmoqoj7cYNixQkextjc9gjLUmYIswkQ=", "P1ReF9wNjagQxi0WiXlTB9uMUIdVi0dLX6yeFMbm2OA=", "r2VLHf/qoxRce8hqcEfndXIhtTQWekFjX10B/4dLx+s=", "jcr8B4jXmrC1/NzWJ6Wx9glQhjnxAaK7f/VY2daQuv8=", "Lfqi6e++OLPX9HCFACovlQV8vKwxgMoL4mTLty8JqtY=", "ZJjLZ4mcLBkLf2m0akn5auJ4u3r7JfhbkTug3JdGgq4=", "J9jSE4DNngXcORKhKx9F8nuruszw/tKUQqHjhW/1/PY=", "ZoITUcrucR+2fApp+DavtkN7l6/zvTqstV+Czaji1Wo=", "2qjrsxQvUl+/M9498Wql8F5mdNHoURhEYx8f69g1YvY=", "2RaoSoXIXDGpFkYxfXmbUTi1+D/5kKK/2pCzqKu4oPY=", "a0mTnVMWcsJAVojbLvN1Es1i/DkeRApNxvgFAebF9/g=", "DdNNqpRB9sSWzqOTdQkk6DjK7OpkFwkIj3AtPs65BJc=", "gkHh2eYMs9C1IYa0s/XsqwMjah3nLhd5x6gG6PzdhaY=", "/NDDimoNcz2JgDUhBtovc5jB+BRGfZYjFDvEiosryjM=", "poxsaEKclA8JoTeSCWkgsrvoRnNrV7CuvHRPgNrj0Ck=", "y+YXvBMOQufrfwhXaL4GdDdL4tCB7f85grcU5LjdSUo=", "1r5g0xh9EKU23K3Tsa37QAjJP8t5RmQ39B1P4YZcpKU="], "CachedAssets": {"+AAIIgt2CT5RqLnsuxp82Ckn+ZbBL4qH37RVsS3M3iw=": {"Identity": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\css\\app.css", "SourceId": "EmployeeRatingSystem.Blazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\rating\\blazor_app\\wwwroot\\", "BasePath": "_content/EmployeeRatingSystem.Blazor", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8ypvqvtjis", "Integrity": "IFsQYFnwjncgSM2nUAxfJVCaEo3LyqEIZY5/ZpLSmt8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 20429, "LastWriteTime": "2025-07-20T08:42:42.3709763+00:00"}}, "CachedCopyCandidates": {}}